import React from 'react';
import { Toast, Modal } from 'dingtalk-design-mobile';
import {
  LikeOutlined,
  LikeFilled,
  DislikeOutlined,
  DislikeFilled,
  RefreshOutlined,
  DownloadAndSaveOutlined,
  DeleteOutlined,
} from '@ali/ding-icons';
import { i18next } from '@ali/dingtalk-i18n';
import OptimizedImage from '@/components/OptimizedImage';
import { rateImage, deleteImage } from '@/apis/image';
import { isMobileDevice } from '@/utils/jsapi';
import { formatCompletionTime } from '@/utils/util';
import { downloadFile } from '@/utils/download';
import './index.less';

// 圆形进度条组件 - Circular progress component for image generation
const CircularProgress: React.FC<{progress: number}> = ({ progress }) => {
  return (
    <div className="circular-progress">
      <svg className="progress-ring" width="60" height="60">
        <circle
          className="progress-ring-circle"
          stroke="currentColor"
          strokeWidth="4"
          fill="transparent"
          r="26"
          cx="30"
          cy="30"
          style={{
            strokeDasharray: `${2 * Math.PI * 26}`,
            strokeDashoffset: `${2 * Math.PI * 26 * (1 - progress / 100)}`,
          }}
        />
      </svg>
      <div className="progress-text">{Math.round(progress)}%</div>
    </div>
  );
};

// Image information interface
interface ImageInfo {
  uuid: string;
  imgUrls: string[]; // 包含4张图片的数组 - Array containing 4 images
  originImgUrl?: string; // 仅当生成类型时hd时有效，用于图片像素对比
  type: 'normal' | 'hd'; // 普通图片normal,高清图片：hd
  userRating?: string;
  status: string;
  errorMsg?: string;
  createAt: string;
  finishTime?: string;
  imageFinishTime?: string; // 图片生成完成时间 - Image generation completion time
  // 前端扩展字段
  prompt?: string;
  negativePrompt?: string;
  size?: string;
  background?: string;
  progress?: number;
  liked?: boolean;
  disliked?: boolean; // 添加点踩状态 - Add dislike state
}

interface ImageItemProps {
  imageInfo: ImageInfo;
  onRegenerate: (imageInfo: ImageInfo) => void;
  showPromptHeader?: boolean; // Whether to show the prompt header
  className?: string;
  progress?: number; // Image generation progress (0-100)
  loadImageList: () => void; // Load image list
}

const ImageItem: React.FC<ImageItemProps> = ({
  imageInfo,
  onRegenerate,
  showPromptHeader = true,
  className = '',
  progress = 0,
  loadImageList,
}) => {
  // Check if current device is mobile
  const isMobile = isMobileDevice();
  // Handle like/unlike - 处理点赞/取消点赞
  const handleLike = async () => {
    try {
      const newLikedState = !imageInfo.liked;
      await rateImage({
        uuid: imageInfo.uuid,
        rating: newLikedState ? 'like' : 'unlike',
      });

      // Update local state (this would typically be handled by parent component)
      Toast.success({
        content: newLikedState ? i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Liked') : i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_LikeCancelled'),
        position: 'top',
        duration: 1,
        maskClickable: true,
      });

      // Refresh the image list to update the state
      loadImageList();
    } catch (err) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_OperationFailed'),
        position: 'top',
        duration: 2,
        maskClickable: true,
      });
    }
  };

  // Handle dislike/unlike - 处理点踩/取消点踩
  const handleDislike = async () => {
    try {
      const newDislikedState = !imageInfo.disliked;
      await rateImage({
        uuid: imageInfo.uuid,
        rating: newDislikedState ? 'dislike' : 'unlike',
      });

      // Update local state (this would typically be handled by parent component)
      Toast.success({
        content: newDislikedState ? i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Disliked') : i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_DislikeCancelled'),
        position: 'top',
        duration: 1,
        maskClickable: true,
      });

      // Refresh the image list to update the state
      loadImageList();
    } catch (err) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_OperationFailed'),
        position: 'top',
        duration: 2,
        maskClickable: true,
      });
    }
  };

  // Handle regenerate
  const handleRegenerate = () => {
    onRegenerate(imageInfo);
  };

  // // Handle prompt toggle
  // const handlePromptToggle = () => {
  //   setIsPromptExpanded(!isPromptExpanded);
  // };

  // Handle download - 下载图片
  const handleDownload = async () => {
    if (imageInfo.imgUrls && imageInfo.imgUrls.length > 0) {
      try {
        // 下载第一张图片，或者可以选择下载所有图片
        const firstImageUrl = imageInfo.imgUrls[0];
        await downloadFile(firstImageUrl, {
          filename: `image_${imageInfo.uuid}.jpg`,
          showProgress: true,
        });
      } catch (error) {
        Toast.fail({
          content: i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_DownloadFailed'),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
      }
    }
  };

  // Handle remove - 删除图片
  const handleRemove = async () => {
    if (!imageInfo.uuid) {
      return;
    }

    // 删除操作之前需要先弹框确认，使用 dingtalk-design-mobile 的 Modal
    Modal.alert(
      i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_ConfirmDeletion'),
      i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_AreYouSureYouWant'),
      [
        {
          text: i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Cancel'),
          onClick: () => {
            // User cancelled deletion - no action needed
          },
        },
        {
          text: i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Delete'),
          style: { color: '#FF0E53' },
          onClick: async () => {
            try {
              const result = await deleteImage({ uuid: imageInfo.uuid });
              if (result?.success) {
                Toast.success({
                  content: i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_DeletedSuccessfully'),
                  duration: 2,
                  position: 'top',
                  maskClickable: true,
                });
                // Delete successfully, refresh image list
                loadImageList();
              } else {
                // Handle API response with success: false
                Toast.fail({
                  content: result?.errorMsg || i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_FailedToDeletePleaseTry'),
                  duration: 3,
                  position: 'top',
                  maskClickable: true,
                });
              }
            } catch (error) {
              Toast.fail({
                content: i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_FailedToDeletePleaseTry'),
                duration: 3,
                position: 'top',
                maskClickable: true,
              });
            }
          },
        },
      ],
    );
  };

  // 计算模糊效果 - Calculate blur effect for progressive image generation
  const getBlurValue = (imageIndex: number, progressValue: number): number => {
    const imageProgress = Math.max(0, Math.min(100, (progressValue - imageIndex * 25) * 4));
    return Math.max(0, 20 - (imageProgress / 100) * 20);
  };

  const currentProgress = progress || imageInfo.progress || 0;
  const isGenerating = imageInfo.status === 'pending' || imageInfo.status === 'processing';
  const isFailed = imageInfo.status === 'failed';
  const isCompleted = imageInfo.status === 'completed';

  return (
    <div className={`image-item ${className}`}>
      {/* Prompt Header - optional */}
      {showPromptHeader && (
        <div className="image-item-header">
          <div className="prompt-info-container">
            <div
              className={'prompt-info expanded'}
            >
              {imageInfo.size} / {imageInfo.prompt}
            </div>
          </div>
        </div>
      )}

      {/* Image Content */}
      <div className="image-content">
        {isCompleted && (
          <div className="image-container">
            <div className="image-grid">
              {imageInfo.imgUrls?.slice(0, 4).map((url) => (
                <OptimizedImage
                  key={`${imageInfo.uuid}-${url}`}
                  src={url}
                  alt="Generated image"
                  className="image-preview"
                />
              ))}
            </div>
          </div>
        )}

        {isGenerating && (
          <div className="image-placeholder">
            <div className="status-overlay">
              <div className="status-processing">
                <div className="generating-text">
                  {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_GeneratingImage')} {Math.round(currentProgress)}%
                </div>
                <CircularProgress progress={currentProgress} />
                <div className="image-grid-generating">
                  {[0, 1, 2, 3].map((index) => (
                    <div
                      key={index}
                      className="image-placeholder-item"
                      style={{
                        filter: `blur(${getBlurValue(index, currentProgress)}px)`,
                        opacity: currentProgress > index * 25 ? 1 : 0.3,
                      }}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {isFailed && (
          <div className="image-placeholder">
            <div className="status-overlay">
              <div className="status-text">{i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_GenerationFailed')}</div>
              <div className="failed-reason">{imageInfo.errorMsg || i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_UnknownError')}</div>
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons - show when image is generated or failed */}
      {(isCompleted || isFailed) && (
        <>
          {/* PC Layout: Action buttons */}
          {!isMobile && (
            <div className="action-section pc-layout">
              <div className="action-buttons">
                {isCompleted && (
                  <>
                    <button className={`action-button ${imageInfo.liked ? 'liked' : ''}`} onClick={handleLike}>
                      {imageInfo.liked ? <LikeFilled /> : <LikeOutlined />}
                    </button>
                    <button className={`action-button ${imageInfo.disliked ? 'disliked' : ''}`} onClick={handleDislike}>
                      {imageInfo.disliked ? <DislikeFilled /> : <DislikeOutlined />}
                    </button>
                    <button className="action-button" onClick={handleDownload}>
                      <DownloadAndSaveOutlined />
                    </button>
                  </>
                )}
                {isFailed && onRegenerate && (
                  <button className="action-button" onClick={handleRegenerate}>
                    <RefreshOutlined />
                  </button>
                )}
                <button className="action-button" onClick={handleRemove}>
                  <DeleteOutlined />
                </button>
              </div>
              {imageInfo.imageFinishTime && imageInfo.status === 'completed' && (
                <div className="completion-time">
                  {formatCompletionTime(new Date(imageInfo.imageFinishTime))}
                </div>
              )}
            </div>
          )}

          {/* Mobile Layout: Action buttons */}
          {isMobile && (
            <>
              <div className="action-buttons mobile-layout">
                {isCompleted && (
                  <>
                    <button className={`action-button ${imageInfo.liked ? 'liked' : ''}`} onClick={handleLike}>
                      {imageInfo.liked ? <LikeFilled /> : <LikeOutlined />}
                    </button>
                    <button className={`action-button ${imageInfo.disliked ? 'disliked' : ''}`} onClick={handleDislike}>
                      {imageInfo.disliked ? <DislikeFilled /> : <DislikeOutlined />}
                    </button>
                    <button className="action-button" onClick={handleDownload}>
                      <DownloadAndSaveOutlined />
                    </button>
                  </>
                )}
                {isFailed && onRegenerate && (
                  <button className="action-button" onClick={handleRegenerate}>
                    <RefreshOutlined />
                  </button>
                )}
                <button className="action-button" onClick={handleRemove}>
                  <DeleteOutlined />
                </button>
              </div>
              {imageInfo.imageFinishTime && imageInfo.status === 'completed' && (
                <div className="completion-time mobile-layout">
                  {formatCompletionTime(new Date(imageInfo.imageFinishTime))}
                </div>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
};

export default ImageItem;
