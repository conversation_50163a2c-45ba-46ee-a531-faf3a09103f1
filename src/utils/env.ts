/**
 * @desc 判断当前是否预发域名
 * <AUTHOR>
 * @return {boolean}
 */
export function isPreEnv() {
  const { host } = window.location;
  return /^pre-n\.dingtalk\.com$/.test(host);
}

/**
 * @desc 判断当前是否7ding.ai预发环境
 * <AUTHOR>
 * @return {boolean}
 */
export function is7dingPreEnv() {
  const { host } = window.location;
  return /^pre-j\.7ding\.ai$/.test(host);
}

export function is7dingProdEnv() {
  const { host } = window.location;
  return /^7ding\.ai$/.test(host);
}

/**
 * @desc 获取7ding.ai对应的域名（根据环境区分）
 * <AUTHOR>
 * @return {string} 返回对应环境的域名
 */
export function get7dingDomain() {
  return isPreEnv() ? 'pre-j.7ding.ai' : '7ding.ai';
}

/**
 * @desc 获取n.dingtalk.com对应的域名（根据环境区分）
 * <AUTHOR>
 * @return {string} 返回对应环境的域名
 */
export function getNdingDomain() {
  return isPreEnv() ? 'https://pre-n.dingtalk.com' : 'https://n.dingtalk.com';
}

/**
 * @desc 获取lwp寻源链接
 * <AUTHOR>
 * @param dspcCode dspcCode
 * @returns {string} 寻源链接
 */
export function getLwpSourceUrl(dspcCode) {
  return `${getNdingDomain()}/dingding/J-dingtalk/productOriginV2/index.html?productId=${dspcCode}`;
}

/** @desc 获取AI图片生成链接
 * <AUTHOR>
 * @returns {string} AI图片生成链接
 */
export function getAIImageUrl() {
  // return `${getNdingDomain()}/dingding/J-dingtalk/ai-image/index.html`;
  return `${getNdingDomain()}/dingding/J-dingtalk/ai-image/index.html`;
}

/** @desc 获取AI视频生成链接
 * <AUTHOR>
 * @returns {string} AI视频生成链接
 */
export function getAIVideoUrl() {
  // return `${getNdingDomain()}/dingding/J-dingtalk/aiVideo/index.html`;
  return `${getNdingDomain()}/dingding/J-dingtalk/aiVideo/index.html`;
}

/**
 * @desc 获取寻源链接
 * <AUTHOR>
 * @param dspcCode dspcCode
 * @returns {string} 寻源链接
 */
export function getSourceUrl(dspcCode) {
  return `https://${get7dingDomain()}/sourcing/${dspcCode}`;
}

/**
 * @desc 获取分享链接
 * <AUTHOR>
 * @returns {string} 分享链接
 */
export function getShareUrl(dspcCode: string): string {
  if (is7dingPreEnv() || is7dingProdEnv()) {
    return window.location.href;
  }
  return getSourceUrl(dspcCode);
}
